import { createRouter, createWebHistory } from 'vue-router'
import { getUserPermissions } from '@/utils/permission'

const routes = [{
		path: '/',
		component: () => import('@/layout/Index.vue'),
		children: [{
			path: '',
			component: () => import('@/components/TabsView.vue'),
			meta: {noLayout: true},
			children: [
				{
					path: '/welcome',
					component: () => import('@/views/Welcome/Index.vue'),
					meta: { title: '欢迎' }
				},
				// 入驻配置
				{
					path: '/SettledConfig/list',
					component: () => import('@/views/SettledConfig/List.vue'),
					meta: { title: '配置列表', permission: 'settled:config:list' }
				},
				{
					path: '/SettledConfig/memberRecord',
					component: () => import('@/views/SettledConfig/MemberRecord.vue'),
					meta: { title: '会员开通记录', permission: 'settled:member:record' }
				},
				// 用户管理
				{
					path: '/user/list',
					component: () => import('@/views/User/List.vue'),
					meta: { title: '用户列表', permission: 'user:list:view' }
				},
				{
					path: '/user/role',
					component: () => import('@/views/User/Role.vue'),
					meta: { title: '角色管理', permission: 'user:role:manage' }
				},
				{
					path: '/user/permission',
					component: () => import('@/views/User/Permission.vue'),
					meta: { title: '权限管理', permission: 'user:permission:manage' }
				},
				// 邀请管理
				{
					path: '/invitation/list',
					component: () => import('@/views/Invitation/List/index.vue'),
					meta: { title: '邀请列表', permission: 'invitation:list:view' }
				},
				// 店铺管理
				{
					path: '/shop/list',
					component: () => import('@/views/Shop/index.vue'),
					meta: { title: '店铺列表', permission: 'shop:list:view' }
				},
				// 选品管理
				{
					path: '/book/selection/center',
					component: () => import('@/views/baseInfo/index.vue'),
					meta: { title: '选品中心', permission: 'book:selection:view' }
				},
				// 仓库管理
				{
					path: '/warehouse/depot/list',
					component: () => import('@/views/Warehouse/Depot/List.vue'),
					meta: { title: '货区管理', permission: 'warehouse:depot:view' }
				},
				// 工具管理
				{
					path: '/tools/cards/list',
					component: () => import('@/views/Tools/Cards/List.vue'),
					meta: { title: '卡密列表', permission: 'cards:list:view' }
				},
				{
					path: '/tools/cards/activeCardsList',
					component: () => import('@/views/Tools/Cards/ActiveCardsList.vue'),
					meta: { title: '活跃卡密列表', permission: 'cards:active:view' }
				},
				// 审核管理
				{
					path: '/examine/violation/list',
					component: () => import('@/views/Examine/Violation/List.vue'),
					meta: { title: '违规列表' }
				},
				// 日志管理
				{
					path: '/log/runningLog/list',
					component: () => import('@/views/Log/RunningLog/List.vue'),
					meta: { title: '运行日志', permission: 'log:running:view' }
				},
				// 任务管理
				{
					path: '/task/list',
					component: () => import('@/views/Task/List.vue'),
					meta: { title: '任务列表', permission: 'task:list:view' }
				},
				// 功能模块
				{
					path: '/useModule/vas/list',
					component: () => import('@/views/UseModule/Vas/List.vue'),
					meta: { title: '服务列表', permission: 'vas:list:view' }
				},
				// 监控中心
				{
					path: '/monitor/dashboard',
					component: () => import('@/views/Monitor/Dashboard.vue'),
					meta: { title: '监控大屏', permission: 'monitor:dashboard:view' }
				},
				// 物流模板
				{
					path: '/warehouse/logistics',
					component: () => import('@/views/logistics/index.vue'),
					meta: { title: '物流模板', permission: 'logistics:view' }
				}

			]
		}]

	},
	{
		path: '/login',
		component: () => import('@/views/Login/Index.vue'),
		meta: { noLayout: true,public: true }
	},
	{
		path: '/redirectUrl',
		component: () => import('@/views/redirectUrl/index.vue'),
		meta: { noLayout: true, title: '用户注册', public: true }
	},

]

// 定义 路由
const router = createRouter({history: createWebHistory(),routes})
// 路由权限守卫
router.beforeEach((to, from, next) => {
	// 检查路由是否需要权限
	if (to.meta && to.meta.permission) {
		const userPermissions = getUserPermissions()
		const requiredPermission = to.meta.permission
		
		if (userPermissions.includes(requiredPermission)) {
			next()
		} else {
			// 没有权限，跳转到403页面或首页
			next('/403')
		}
	} else {
		next()
	}
})
// 返回 模块
export default router
