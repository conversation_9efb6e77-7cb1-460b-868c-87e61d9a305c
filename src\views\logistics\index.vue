<template>
  <div class="logistics-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>物流模板管理</span>
          <el-button type="primary" @click="handleAdd">新增模板</el-button>
        </div>
      </template>

      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="templateName" label="模板名称" />
        <el-table-column prop="deliveryArea" label="发货地区" />
        <el-table-column prop="pricingMethod" label="计价方式" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 运费模板对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="60%">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板名称" prop="templateName">
              <el-input v-model="form.templateName" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发货地区" prop="deliveryArea">
              <el-input v-model="form.deliveryArea" placeholder="请输入发货地区" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计价方式" prop="pricingMethod">
              <el-select v-model="form.pricingMethod" placeholder="请选择计价方式">
                <el-option label="按重量" value="weight" />
                <el-option label="按本数" value="book" />
                <el-option label="按件数" value="piece" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="配送方式" prop="deliveryMethod">
              <el-select v-model="form.deliveryMethod" placeholder="请选择配送方式">
                <el-option label="快递" value="express" />
                <el-option label="物流" value="logistics" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const tableData = ref([
  {
    id: 1,
    templateName: '默认模板',
    deliveryArea: '全国',
    pricingMethod: '按重量'
  }
])

const dialogVisible = ref(false)
const dialogTitle = ref('新增模板')

const form = reactive({
  templateName: '',
  deliveryArea: '',
  pricingMethod: '',
  deliveryMethod: '',
  remark: ''
})

const rules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  deliveryArea: [
    { required: true, message: '请输入发货地区', trigger: 'blur' }
  ]
}

// 方法
const handleAdd = () => {
  dialogTitle.value = '新增模板'
  Object.assign(form, {
    templateName: '',
    deliveryArea: '',
    pricingMethod: '',
    deliveryMethod: '',
    remark: ''
  })
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑模板'
  Object.assign(form, row)
  dialogVisible.value = true
}

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除这个模板吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
  })
}

const handleSave = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.logistics-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}