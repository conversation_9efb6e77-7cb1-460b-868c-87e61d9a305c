<template>
    <!-- 运费模板对话框 -->
    <el-dialog :title="freightDialog.title" :visible.sync="freightDialog.visible" width="80%" :before-close="cancelFreightTemplate">
      <el-form ref="templatesForm" :model="freightForm" :rules="freightRule" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板名称" prop="templateName">
              <el-input v-model="freightForm.templateName" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发货地区" prop="deliveryArea">
              <el-cascader
                v-model="freightForm.deliveryArea"
                :options="areaOptions"
                placeholder="请选择发货地区"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="默认联系人" prop="contact">
              <el-input v-model="freightForm.contact" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话" prop="phoneNumber">
              <el-input v-model="freightForm.phoneNumber" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="详细地址" prop="fullAddress">
              <el-input v-model="freightForm.fullAddress" placeholder="请输入详细地址" />
            </el-form-item>
          </el-col>
        </el-row>
  
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计价方式">
              <el-radio-group v-model="freightForm.pricingMethod">
                <el-radio label="weight">按重量</el-radio>
                <el-radio label="book">按本数</el-radio>
                <el-radio label="piece">按件数</el-radio>
                <el-radio label="custom">自定义</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运送方式">
              <el-radio-group v-model="freightForm.deliveryMethod">
                <el-radio label="express">快递</el-radio>
                <el-radio label="logistics">物流</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
  
        <!-- 运费设置表格 -->
        <el-form-item label="运费设置">
          <el-table :data="deliveryRanges" border style="width: 100%">
            <el-table-column prop="region" label="配送地区" width="300" />
            <el-table-column 
              v-for="column in dynamicColumns" 
              :key="column.prop"
              :prop="column.prop" 
              :label="column.label" 
              :width="column.width"
            >
              <template slot-scope="scope">
                <el-input 
                  v-model.number="scope.row[column.prop]" 
                  :placeholder="column.placeholder"
                  @blur="column.validate ? validateFee(scope.row[column.prop]) : null"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button size="mini" @click="editRegion(scope.row)">编辑</el-button>
                <el-button size="mini" type="danger" @click="deleteRegion(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
  
        <el-form-item label="备注">
          <el-input v-model="freightForm.deliveryNote" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
  
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelFreightTemplate">取消</el-button>
        <el-button type="primary" :loading="buttonLoading" @click="saveFreightTemplate">保存</el-button>
      </div>
    </el-dialog>
  
    <!-- 区域编辑对话框 -->
    <el-dialog title="编辑地区" :visible.sync="regionDialog.visible" width="60%">
      <el-checkbox-group v-model="selectedProvinces">
        <el-row>
          <el-col :span="6" v-for="province in allProvinces" :key="province">
            <el-checkbox :label="province">{{ province }}</el-checkbox>
          </el-col>
        </el-row>
      </el-checkbox-group>
      <div slot="footer" class="dialog-footer">
        <el-button @click="regionDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveRegion">保存</el-button>
      </div>
    </el-dialog>
  </template>
  
  <script>
  import {
    listLogistics,
    getLogistics,
    delLogistics,
    addLogistics,
    updateLogistics,
  } from '@/api/zhishu/logistics';
  import {
    createTemplate,
    getCitiesByProvinceId,
    getDistrictsByCityId,
    getFreInfo,
    getProvinces,
    UpdateTemplate
  } from '@/api/zhishu/district';
  import { depotNameList } from '@/api/zhishu/shelves';
  
  export default {
    name: "Logistics",
    data() {
      return {
        curruntRow: null,
        isValid: true,
        buttonLoading: false,
        areaOptions: [],
        
        // 运费模板对话框状态
        freightDialog: {
          visible: false,
          title: '运费模板设置'
        },
        
        // 运费模板表单数据
        freightForm: {
          templateName: '',
          contact: '',
          phoneNumber: '',
          fullAddress: '',
          deliveryArea: [],
          pricingMethod: 'weight',
          deliveryMethod: 'express',
          deliveryNote: ''
        },
        
        // 表单验证规则
        freightRule: {
          contact: [
            { required: true, message: '请输入默认联系人', trigger: 'blur' }
          ],
          phoneNumber: [
            { required: true, message: '请输入联系电话', trigger: 'blur' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
          ],
          fullAddress: [
            { required: true, message: '请输入详细地址', trigger: 'blur' }
          ]
        },
        
        // 默认运送范围数据
        defaultDeliveryRanges: [
          {
            region: '河北',
            firstWeight: 1.0,
            firstFee: 1.0,
            cities: ['保定', '沧州', '承德', '邯郸', '衡水', '廊坊', '秦皇岛', '石家庄', '唐山', '邢台', '张家口'],
            continueWeight: 1.0,
            continueFee: 1.0
          },
          {
            region: '北京',
            firstWeight: 1.0,
            firstFee: 1.0,
            continueWeight: 1.0,
            continueFee: 1.0
          },
          {
            region: '天津',
            firstWeight: 1.0,
            firstFee: 1.0,
            continueWeight: 1.0,
            continueFee: 1.0
          },
          {
            region: '重庆',
            firstWeight: 1.0,
            firstFee: 1.0,
            continueWeight: 1.0,
            continueFee: 1.0
          },
          {
            region: '上海、江苏、浙江、安徽、江西、山西、山东、内蒙古、湖南、湖北、河南、广东、广西、福建、海南、辽宁、吉林、黑龙江、陕西、云南、贵州、四川',
            firstWeight: 1.0,
            firstFee: 1.0,
            continueWeight: 1.0,
            continueFee: 1.0
          },
          {
            region: '甘肃、宁夏、青海',
            firstWeight: 1.0,
            firstFee: 1.0,
            continueWeight: 1.0,
            continueFee: 1.0
          },
          {
            region: '新疆、西藏',
            firstWeight: 1.0,
            firstFee: 1.0,
            continueWeight: 1.0,
            continueFee: 1.0
          },
          {
            region: '香港、澳门、台湾、海外',
            firstWeight: 1.0,
            firstFee: 1.0,
            continueWeight: 1.0,
            continueFee: 1.0
          }
        ],
        
        // 运送范围数据
        deliveryRanges: [],
        
        // 所有省份
        allProvinces: ['北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江', '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南', '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州', '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆', '香港', '澳门', '台湾'],
        
        // 区域编辑对话框状态
        regionDialog: {
          visible: false,
          title: '编辑地区',
          currentRegion: ''
        },
        
        // 当前选中的省份列表
        selectedProvinces: [],
        
        // 其他数据...
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          // ... 其他查询参数
        },
        form: {
          // ... 表单数据
        }
      };
    },
    
    computed: {
      // 根据计价方式展示不同内容
      dynamicColumns() {
        const method = this.freightForm.pricingMethod;
  
        if (method === 'weight') {
          return [
            { prop: 'firstWeight', label: '首重（千克）', placeholder: '请输入首重', width: '120' },
            { prop: 'firstFee', label: '首费（元）', placeholder: '请输入首费', width: '120', validate: true },
            { prop: 'continueWeight', label: '续重（千克）', placeholder: '请输入续重', width: '120' },
            { prop: 'continueFee', label: '续费（元）', placeholder: '请输入续费', width: '120' },
          ];
        } else if (method === 'book') {
          return [
            { prop: 'firstWeight', label: '首重本数（本）', placeholder: '请输入首本', width: '120' },
            { prop: 'firstFee', label: '首费（元）', placeholder: '请输入首费', width: '120', validate: true },
            { prop: 'continueWeight', label: '续重本数（本）', placeholder: '请输入续本', width: '120' },
            { prop: 'continueFee', label: '续费（元）', placeholder: '请输入续费', width: '120' },
          ];
        } else if (method === 'piece') {
          return [
            { prop: 'firstWeight', label: '首件数（件）', placeholder: '请输入首件', width: '120' },
            { prop: 'firstFee', label: '首费（元）', placeholder: '请输入首费', width: '120', validate: true },
            { prop: 'continueWeight', label: '续件数（件）', placeholder: '请输入续件', width: '120' },
            { prop: 'continueFee', label: '续费（元）', placeholder: '请输入续费', width: '120' },
          ];
        } else if (method === 'custom') {
          return [
            { prop: 'firstFee', label: '运费（元）', placeholder: '请输入运费', width: '120', validate: true },
          ];
        }
        return [];
      }
    },
    
    mounted() {
      this.deliveryRanges = JSON.parse(JSON.stringify(this.defaultDeliveryRanges));
    },
    
    methods: {
      // 验证费用
      validateFee(value) {
        if (value === null || value === '' || value === undefined) {
          this.isValid = false;
          this.$message.error('首费/运费不能为空');
        } else {
          this.isValid = true;
        }
      },
      
      // 打开运费模板对话框
      async handleFreightTemplate(row) {
        this.freightDialog.visible = true;
        
        try {
          const res = await getFreInfo(row.id);
          console.log(row.id);
          
          this.freightForm.templateName = row.templateName;
          const { deliveryProvince: pro, deliveryCity: city, deliveryArea: area } = res;
          this.freightForm.deliveryArea = [Number(pro), Number(city), Number(area)];
          
          // 数字编码转换对应的计价方式
          const pricingMethodMap = {
            0: 'weight',
            1: 'book',
            2: 'piece',
            3: 'custom'
          };
          this.freightForm.pricingMethod = pricingMethodMap[row.pricingMethod];
          
          // 数字编码对应运送方式
          const deliveryMethodMap = {
            0: 'express',
            1: 'logistics'
          };
          this.freightForm.deliveryMethod = deliveryMethodMap[row.shipping];
          
          if (row.shippingRange != null) {
            const parsedData = JSON.parse(row.shippingRange);
            this.deliveryRanges = this.transformData(parsedData);
          } else {
            this.deliveryRanges = JSON.parse(JSON.stringify(this.defaultDeliveryRanges));
          }
          
          this.freightForm.contact = res.contact;
          this.freightForm.phoneNumber = res.phoneNumber;
          this.freightForm.fullAddress = res.fullAddress;
          this.freightForm.deliveryNote = res.remark;
          this.curruntRow = row;
          
          console.log(this.freightForm);
        } catch (error) {
          console.error('获取运费信息失败:', error);
        }
      },
      
      // 转换数据格式
      transformData(dataRange) {
        const result = [];
        for (const region in dataRange) {
          const [firstWeight, firstFee, continueWeight, continueFee] = dataRange[region];
          result.push({
            region,
            firstWeight: parseFloat(firstWeight) || 0.0,
            firstFee: parseFloat(firstFee) || 0.0,
            continueWeight: parseFloat(continueWeight) || 0.0,
            continueFee: parseFloat(continueFee) || 0.0
          });
        }
        return result;
      },
      
      // 编辑地区
      editRegion(row) {
        this.regionDialog.visible = true;
        this.regionDialog.currentRegion = row.region;
        this.selectedProvinces = row.region.split('、');
      },
      
      // 删除地区
      deleteRegion(row) {
        this.$confirm('确定要删除该地区吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const index = this.deliveryRanges.findIndex(item => item.region === row.region);
          if (index !== -1) {
            this.deliveryRanges.splice(index, 1);
            this.$message.success('删除成功');
          }
        }).catch(() => {});
      },
      
      // 保存运费模板
      async saveFreightTemplate() {
        try {
          const valid = await this.$refs.templatesForm.validate();
          if (valid) {
            this.buttonLoading = true;
            
            const [provinceId, cityId, districtId] = this.freightForm.deliveryArea;
            
            const pricingMethodMap = {
              'weight': 0,
              'book': 1,
              'piece': 2,
              'custom': 3
            };
            
            const deliveryMethodMap = {
              'express': 0,
              'logistics': 1
            };
            
            const shippingRanges = this.deliveryRanges.reduce((acc, range) => {
              acc[range.region] = [range.firstWeight, range.firstFee, range.continueWeight, range.continueFee];
              return acc;
            }, {});
            
            const templateData = {
              id: this.curruntRow.id,
              template_name: this.freightForm.templateName,
              contact: this.freightForm.contact,
              phoneNumber: this.freightForm.phoneNumber,
              fullAddress: this.freightForm.fullAddress,
              delivery_province: provinceId,
              delivery_city: cityId,
              delivery_area: districtId,
              pricing_method: pricingMethodMap[this.freightForm.pricingMethod],
              shipping: deliveryMethodMap[this.freightForm.deliveryMethod],
              shipping_range: shippingRanges,
              warehouse_id: this.curruntRow.id,
              remark: this.freightForm.deliveryNote
            };
            
            if (this.isValid) {
              await UpdateTemplate(templateData);
              this.$message.success('运费模板保存成功');
              this.freightDialog.visible = false;
            } else {
              this.$message.error('首费/运费不能为空');
            }
          }
        } catch (error) {
          this.$message.error('运费模板保存失败，请检查输入的信息');
        } finally {
          this.buttonLoading = false;
          // this.getList(); // 如果有列表刷新方法
        }
      },
      
      // 取消运费模板
      cancelFreightTemplate() {
        this.freightDialog.visible = false;
      },
      
      // 保存区域设置
      handleSaveRegion() {
        if (this.selectedProvinces.length === 0) {
          this.$message.error('请至少选择一个省份');
          return;
        }
        
        // 更新对应地区的省份信息
        const targetIndex = this.deliveryRanges.findIndex(
          item => item.region === this.regionDialog.currentRegion
        );
        
        if (targetIndex !== -1) {
          this.$set(this.deliveryRanges, targetIndex, {
            ...this.deliveryRanges[targetIndex],
            region: this.selectedProvinces.join('、')
          });
        }
        
        this.regionDialog.visible = false;
        this.$message.success('地区设置保存成功');
      }
    }
  };
  </script>